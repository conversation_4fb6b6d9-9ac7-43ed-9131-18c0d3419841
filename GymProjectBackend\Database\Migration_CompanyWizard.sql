-- Company Unified Add Wizard Migration Script
-- Bu script, mevcut veritabanında herhangi bir değişiklik yapmaz
-- Sadece yeni wizard sisteminin çalışması için gerekli kontrolleri yapar

-- 1. User tablosunda RequirePasswordChange alanının varlığını kontrol et
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
               WHERE TABLE_NAME = 'Users' AND COLUMN_NAME = 'RequirePasswordChange')
BEGIN
    ALTER TABLE Users ADD RequirePasswordChange BIT NOT NULL DEFAULT 0;
    PRINT 'RequirePasswordChange column added to Users table';
END
ELSE
BEGIN
    PRINT 'RequirePasswordChange column already exists in Users table';
END

-- 2. OperationClaims tablosunda 'owner' rolünün varlığını kontrol et
IF NOT EXISTS (SELECT * FROM OperationClaims WHERE Name = 'owner')
BEGIN
    INSERT INTO OperationClaims (Name, IsActive, CreationDate) 
    VALUES ('owner', 1, GETDATE());
    PRINT 'Owner role added to OperationClaims table';
END
ELSE
BEGIN
    PRINT 'Owner role already exists in OperationClaims table';
END

-- 3. OperationClaims tablosunda 'member' rolünün varlığını kontrol et
IF NOT EXISTS (SELECT * FROM OperationClaims WHERE Name = 'member')
BEGIN
    INSERT INTO OperationClaims (Name, IsActive, CreationDate) 
    VALUES ('member', 1, GETDATE());
    PRINT 'Member role added to OperationClaims table';
END
ELSE
BEGIN
    PRINT 'Member role already exists in OperationClaims table';
END

-- 4. Mevcut tabloların yapısını kontrol et
PRINT 'Checking existing table structures...';

-- Users tablosu kontrol
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'Users')
BEGIN
    PRINT 'Users table exists';
    SELECT 'Users' as TableName, COLUMN_NAME, DATA_TYPE, IS_NULLABLE 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'Users'
    ORDER BY ORDINAL_POSITION;
END

-- Companies tablosu kontrol
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'Companies')
BEGIN
    PRINT 'Companies table exists';
END

-- CompanyAddresses tablosu kontrol
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'CompanyAddresses')
BEGIN
    PRINT 'CompanyAddresses table exists';
END

-- CompanyUsers tablosu kontrol
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'CompanyUsers')
BEGIN
    PRINT 'CompanyUsers table exists';
END

-- UserCompanies tablosu kontrol
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'UserCompanies')
BEGIN
    PRINT 'UserCompanies table exists';
END

-- Cities tablosu kontrol
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'Cities')
BEGIN
    PRINT 'Cities table exists';
END

-- Towns tablosu kontrol
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'Towns')
BEGIN
    PRINT 'Towns table exists';
END

PRINT 'Migration script completed successfully!';
PRINT 'Company Unified Add Wizard is ready to use.';
