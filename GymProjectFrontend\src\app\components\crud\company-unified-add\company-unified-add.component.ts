import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { Router } from '@angular/router';
import { CompanyService } from '../../../services/company.service';
import { CompanyadressService } from '../../../services/companyadress.service';
import { CompanyUserService } from '../../../services/company-user.service';
import { UserCompanyService } from '../../../services/usercompany.service';
import { CityService } from '../../../services/city.service';
import { TownService } from '../../../services/town.service';
import { AuthService } from '../../../services/auth.service';
import { City } from '../../../models/city';
import { Town } from '../../../models/town';
import { Company } from '../../../models/company';
import { CompanyAdress } from '../../../models/companyAdress';
import { CompanyUser } from '../../../models/companyUser';
import { UserCompany } from '../../../models/usercompany';
import { faArrowLeft, faArrowRight, faCheck, faInfoCircle, faBuilding, faMapMarkerAlt, faUserTie, faEye } from '@fortawesome/free-solid-svg-icons';
import { Observable } from 'rxjs';
import { startWith, map } from 'rxjs/operators';

// Wizard adımları enum'u
enum WizardStep {
  COMPANY_INFO = 1,
  ADDRESS_INFO = 2,
  OWNER_INFO = 3,
  PREVIEW = 4
}

@Component({
  selector: 'app-company-unified-add',
  templateUrl: './company-unified-add.component.html',
  styleUrls: ['./company-unified-add.component.css'],
  standalone: false
})
export class CompanyUnifiedAddComponent implements OnInit {
  // Icons
  faArrowLeft = faArrowLeft;
  faArrowRight = faArrowRight;
  faCheck = faCheck;
  faInfoCircle = faInfoCircle;
  faBuilding = faBuilding;
  faMapMarkerAlt = faMapMarkerAlt;
  faUserTie = faUserTie;
  faEye = faEye;

  // Wizard state
  currentStep: WizardStep = WizardStep.COMPANY_INFO;
  WizardStep = WizardStep; // Template'de kullanmak için

  // Forms
  companyForm!: FormGroup;
  addressForm!: FormGroup;
  ownerForm!: FormGroup;
  isSubmitting = false;

  // Data
  cities: City[] = [];
  towns: Town[] = [];
  filteredCities: Observable<City[]>;
  filteredTowns: Observable<Town[]>;

  // Basit ilerleme sistemi
  completedSteps: Set<WizardStep> = new Set();

  // Salon data
  salonData: any = {};

  // Loading states
  loadingMessage: string = '';
  isLoadingCities: boolean = false;
  isLoadingTowns: boolean = false;

  constructor(
    private formBuilder: FormBuilder,
    private companyService: CompanyService,
    private companyAdressService: CompanyadressService,
    private companyUserService: CompanyUserService,
    private userCompanyService: UserCompanyService,
    private cityService: CityService,
    private townService: TownService,
    private toastrService: ToastrService,
    private authService: AuthService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.initializeForms();
    this.getCities();
  }

  initializeForms(): void {
    // Adım 1: Şirket bilgileri formu
    this.companyForm = this.formBuilder.group({
      companyName: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(100)]],
      companyPhone: ['', [Validators.required, Validators.pattern(/^0[0-9]{10}$/)]]
    });

    // Adım 2: Adres bilgileri formu
    this.addressForm = this.formBuilder.group({
      city: ['', Validators.required],
      town: ['', Validators.required],
      address: ['', [Validators.required, Validators.maxLength(500)]]
    });

    // Adım 3: Salon sahibi bilgileri formu
    this.ownerForm = this.formBuilder.group({
      ownerName: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(100)]],
      ownerPhone: ['', [Validators.required, Validators.pattern(/^0[0-9]{10}$/)]],
      ownerEmail: ['', [Validators.required, Validators.email]]
    });

    // Autocomplete setup
    this.setupAutocomplete();
  }

  setupAutocomplete(): void {
    this.filteredCities = this.addressForm.get('city')!.valueChanges.pipe(
      startWith(''),
      map(value => typeof value === 'string' ? value : value?.cityName),
      map(cityName => cityName ? this._filterCities(cityName) : this.cities.slice())
    );

    this.filteredTowns = this.addressForm.get('town')!.valueChanges.pipe(
      startWith(''),
      map(value => typeof value === 'string' ? value : value?.townName),
      map(townName => townName ? this._filterTowns(townName) : this.towns.slice())
    );

    // Real-time validasyon setup
    this.setupRealTimeValidation();
  }

  setupRealTimeValidation(): void {
    // Şirket telefonu real-time validasyon
    this.companyForm.get('companyPhone')?.valueChanges.subscribe(value => {
      this.validatePhoneRealTime(this.companyForm, 'companyPhone', value);
    });

    // Salon sahibi telefonu real-time validasyon
    this.ownerForm.get('ownerPhone')?.valueChanges.subscribe(value => {
      this.validatePhoneRealTime(this.ownerForm, 'ownerPhone', value);
    });

    // Email real-time validasyon
    this.ownerForm.get('ownerEmail')?.valueChanges.subscribe(value => {
      this.validateEmailRealTime(value);
    });
  }

  validatePhoneRealTime(form: FormGroup, fieldName: string, value: string): void {
    if (!value) return;

    const phonePattern = /^0[0-9]{10}$/;
    const control = form.get(fieldName);

    if (control && value.length > 0) {
      if (!phonePattern.test(value)) {
        // Geçersiz format - görsel feedback
        control.setErrors({ ...control.errors, pattern: true });
      }
    }
  }

  validateEmailRealTime(value: string): void {
    if (!value) return;

    const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const control = this.ownerForm.get('ownerEmail');

    if (control && value.length > 0) {
      if (!emailPattern.test(value)) {
        // Geçersiz format - görsel feedback
        control.setErrors({ ...control.errors, email: true });
      }
    }
  }

  // Wizard navigation methods
  nextStep(): void {
    if (this.canProceedToNextStep()) {
      // Mevcut adımı tamamlandı olarak işaretle
      this.completedSteps.add(this.currentStep);

      if (this.currentStep < WizardStep.PREVIEW) {
        this.currentStep++;
        this.onStepChange();
      }
    } else {
      this.toastrService.error('Lütfen işaretli alanları doldurunuz', 'Eksik Bilgi');
      this.validateAndHighlightErrors();
    }
  }

  previousStep(): void {
    if (this.currentStep > WizardStep.COMPANY_INFO) {
      this.currentStep--;
    }
  }

  goToStep(step: WizardStep): void {
    // Basit kontrol: sadece tamamlanmış adımlara veya bir sonraki adıma gidebilir
    if (this.canGoToStep(step)) {
      this.currentStep = step;
      this.onStepChange();
    }
  }

  // Bir adıma gidilip gidilemeyeceğini kontrol et
  canGoToStep(step: WizardStep): boolean {
    // Mevcut adıma her zaman gidebilir
    if (step === this.currentStep) return true;

    // Tamamlanmış adımlara gidebilir
    if (this.completedSteps.has(step)) return true;

    // Bir sonraki adıma gidebilir mi kontrol et
    const previousStep = step - 1;
    if (previousStep >= WizardStep.COMPANY_INFO) {
      return this.completedSteps.has(previousStep);
    }

    // İlk adıma her zaman gidebilir
    return step === WizardStep.COMPANY_INFO;
  }

  onStepChange(): void {
    switch (this.currentStep) {
      case WizardStep.ADDRESS_INFO:
        this.saveCompanyInfo();
        break;
      case WizardStep.OWNER_INFO:
        this.saveAddressInfo();
        break;
      case WizardStep.PREVIEW:
        this.saveOwnerInfo();
        this.preparePreviewData();
        break;
    }
  }

  // Step validation methods
  canProceedToNextStep(): boolean {
    switch (this.currentStep) {
      case WizardStep.COMPANY_INFO:
        return this.companyForm.valid;
      case WizardStep.ADDRESS_INFO:
        return this.addressForm.valid;
      case WizardStep.OWNER_INFO:
        return this.ownerForm.valid;
      default:
        return true;
    }
  }

  // Step completion check
  isStepCompleted(step: WizardStep): boolean {
    return this.completedSteps.has(step);
  }

  getStepButtonClass(step: WizardStep): string {
    if (this.currentStep === step) {
      return 'btn-primary step-nav-btn active';
    } else if (this.isStepCompleted(step)) {
      return 'btn-success step-nav-btn completed';
    } else if (this.canGoToStep(step)) {
      return 'btn-outline-primary step-nav-btn';
    } else {
      return 'btn-outline-secondary step-nav-btn';
    }
  }

  getStepIcon(step: WizardStep): any {
    // Tamamlanmış adımlar için check ikonu
    if (this.isStepCompleted(step)) {
      return this.faCheck;
    }

    // Aktif veya henüz tamamlanmamış adımlar için varsayılan ikon
    switch (step) {
      case WizardStep.COMPANY_INFO:
        return this.faBuilding;
      case WizardStep.ADDRESS_INFO:
        return this.faMapMarkerAlt;
      case WizardStep.OWNER_INFO:
        return this.faUserTie;
      case WizardStep.PREVIEW:
        return this.faEye;
      default:
        return this.faInfoCircle;
    }
  }

  // Data saving methods
  saveCompanyInfo(): void {
    if (this.companyForm.valid) {
      this.salonData = {
        ...this.salonData,
        ...this.companyForm.value
      };
    }
  }

  saveAddressInfo(): void {
    if (this.addressForm.valid) {
      this.salonData = {
        ...this.salonData,
        ...this.addressForm.value
      };
    }
  }

  saveOwnerInfo(): void {
    if (this.ownerForm.valid) {
      this.salonData = {
        ...this.salonData,
        ...this.ownerForm.value
      };
    }
  }

  preparePreviewData(): void {
    this.saveOwnerInfo();
    // Preview için tüm verileri hazırla
  }

  // Cities and Towns methods
  getCities(): void {
    this.isLoadingCities = true;
    this.cityService.getCities().subscribe(
      response => {
        if (response.success) {
          this.cities = response.data;
        }
        this.isLoadingCities = false;
      },
      error => {
        this.toastrService.error('Şehirler yüklenirken hata oluştu', 'Hata');
        this.isLoadingCities = false;
      }
    );
  }

  onCitySelected(city: City): void {
    this.addressForm.patchValue({ town: '' });
    this.getTowns(city.cityID);
  }

  onCityChange(): void {
    const cityControl = this.addressForm.get('city');
    if (cityControl && cityControl.value && cityControl.value.cityID) {
      this.onCitySelected(cityControl.value);
    }
  }

  getTowns(cityId: number): void {
    this.isLoadingTowns = true;
    this.townService.getTownsByCityId(cityId).subscribe(
      response => {
        if (response.success) {
          this.towns = response.data;
        }
        this.isLoadingTowns = false;
      },
      error => {
        this.toastrService.error('İlçeler yüklenirken hata oluştu', 'Hata');
        this.isLoadingTowns = false;
      }
    );
  }

  // Filter methods
  private _filterCities(value: string): City[] {
    const filterValue = value.toLowerCase();
    return this.cities.filter(city => city.cityName.toLowerCase().includes(filterValue));
  }

  private _filterTowns(value: string): Town[] {
    const filterValue = value.toLowerCase();
    return this.towns.filter(town => town.townName.toLowerCase().includes(filterValue));
  }

  // Display methods for autocomplete
  displayCity(city: City): string {
    return city && city.cityName ? city.cityName : '';
  }

  displayTown(town: Town): string {
    return town && town.townName ? town.townName : '';
  }

  // Progress calculation
  getProgress(): number {
    return (this.completedSteps.size / 4) * 100;
  }

  getStepTitle(): string {
    switch (this.currentStep) {
      case WizardStep.COMPANY_INFO:
        return 'Şirket Bilgileri';
      case WizardStep.ADDRESS_INFO:
        return 'Adres Bilgileri';
      case WizardStep.OWNER_INFO:
        return 'Salon Sahibi Bilgileri';
      case WizardStep.PREVIEW:
        return 'Önizleme & Kaydet';
      default:
        return '';
    }
  }

  getStepDescription(): string {
    switch (this.currentStep) {
      case WizardStep.COMPANY_INFO:
        return 'Şirket adı ve telefon bilgilerini girin';
      case WizardStep.ADDRESS_INFO:
        return 'Şirket adres bilgilerini girin';
      case WizardStep.OWNER_INFO:
        return 'Salon sahibi bilgilerini girin';
      case WizardStep.PREVIEW:
        return 'Bilgileri gözden geçirin ve salonu kaydedin';
      default:
        return '';
    }
  }

  // Validation ve Error Highlighting Sistemi
  validateAndHighlightErrors(): void {
    const missingFields: string[] = [];

    switch (this.currentStep) {
      case WizardStep.COMPANY_INFO:
        this.markFormGroupTouched(this.companyForm);
        break;
      case WizardStep.ADDRESS_INFO:
        this.markFormGroupTouched(this.addressForm);
        break;
      case WizardStep.OWNER_INFO:
        this.markFormGroupTouched(this.ownerForm);
        break;
    }
  }

  markFormGroupTouched(formGroup: FormGroup): void {
    Object.keys(formGroup.controls).forEach(key => {
      const control = formGroup.get(key);
      control?.markAsTouched();
    });
  }

  goBack(): void {
    this.router.navigate(['/company']);
  }

  // Final submission
  onSubmit(): void {
    if (this.canSubmitSalon()) {
      this.isSubmitting = true;
      this.loadingMessage = 'E-posta kontrolü yapılıyor...';

      // Email kontrolü yap
      this.authService.UserExists(this.salonData.ownerEmail).subscribe(
        (userExistsResponse) => {
          if (!userExistsResponse.success) {
            // Email çakışması var - 3. adıma git
            this.currentStep = WizardStep.OWNER_INFO;
            this.completedSteps.delete(WizardStep.OWNER_INFO);
            this.completedSteps.delete(WizardStep.PREVIEW);
            this.isSubmitting = false;
            this.toastrService.error('Bu e-posta adresi zaten kullanımda', 'E-posta Çakışması');

            // Email field'ını invalid olarak işaretle
            const emailControl = this.ownerForm.get('ownerEmail');
            if (emailControl) {
              emailControl.setErrors({ emailExists: true });
              emailControl.markAsTouched();
            }
            return;
          }

          // Email uygun, salon sahibi kaydını başlat
          this.registerOwnerAndCreateSalon();
        },
        (error) => {
          this.isSubmitting = false;
          this.toastrService.error('E-posta kontrolü yapılırken hata oluştu', 'Hata');
        }
      );
    }
  }

  private registerOwnerAndCreateSalon(): void {
    this.loadingMessage = 'Salon sahibi kaydediliyor...';

    // 1. Salon sahibini kaydet
    const ownerRegisterModel = {
      fullName: this.salonData.ownerName,
      email: this.salonData.ownerEmail,
      phoneNumber: this.salonData.ownerPhone,
      cityID: this.salonData.city.cityID,
      townID: this.salonData.town.townID
    };

    this.authService.registerOwner(ownerRegisterModel).subscribe(
      (ownerResponse) => {
        if (ownerResponse.success) {
          // 2. Şirketi kaydet
          this.createCompany(ownerResponse.data.userId);
        } else {
          this.handleError('Salon sahibi kaydı başarısız', WizardStep.OWNER_INFO);
        }
      },
      (error) => {
        this.handleError('Salon sahibi kaydı başarısız: ' + (error.message || 'Bilinmeyen hata'), WizardStep.OWNER_INFO);
      }
    );
  }

  private createCompany(userId: number): void {
    this.loadingMessage = 'Şirket kaydediliyor...';

    const companyModel = {
      companyName: this.salonData.companyName,
      phoneNumber: this.salonData.companyPhone,
      isActive: true
    } as Company;

    this.companyService.add(companyModel).subscribe(
      (companyResponse) => {
        if (companyResponse.success) {
          // Şirket ID'sini al ve adres kaydet
          this.getCompanyIdAndCreateAddress(companyModel.companyName, userId);
        } else {
          this.handleError('Şirket kaydı başarısız', WizardStep.COMPANY_INFO);
        }
      },
      (error) => {
        this.handleError('Şirket kaydı başarısız', WizardStep.COMPANY_INFO);
      }
    );
  }

  private getCompanyIdAndCreateAddress(companyName: string, userId: number): void {
    this.companyService.getCompanies().subscribe(
      (companiesResponse) => {
        if (companiesResponse.success) {
          const addedCompany = companiesResponse.data.find(c => c.companyName === companyName);
          if (addedCompany) {
            this.createCompanyAddress(addedCompany.companyID, userId);
          } else {
            this.handleError('Şirket bulunamadı', WizardStep.COMPANY_INFO);
          }
        } else {
          this.handleError('Şirket listesi alınamadı', WizardStep.COMPANY_INFO);
        }
      },
      (error) => {
        this.handleError('Şirket listesi alınamadı', WizardStep.COMPANY_INFO);
      }
    );
  }

  private createCompanyAddress(companyId: number, userId: number): void {
    this.loadingMessage = 'Adres bilgileri kaydediliyor...';

    const addressModel = {
      companyID: companyId,
      cityID: this.salonData.city.cityID,
      townID: this.salonData.town.townID,
      adress: this.salonData.address
    } as CompanyAdress;

    this.companyAdressService.add(addressModel).subscribe(
      (addressResponse) => {
        if (addressResponse.success) {
          this.createCompanyUser(companyId, userId);
        } else {
          this.handleError('Adres kaydı başarısız', WizardStep.ADDRESS_INFO);
        }
      },
      (error) => {
        this.handleError('Adres kaydı başarısız', WizardStep.ADDRESS_INFO);
      }
    );
  }

  private createCompanyUser(companyId: number, userId: number): void {
    this.loadingMessage = 'Salon sahibi bilgileri kaydediliyor...';

    const ownerModel = {
      cityID: this.salonData.city.cityID,
      townID: this.salonData.town.townID,
      name: this.salonData.ownerName,
      phoneNumber: this.salonData.ownerPhone,
      email: this.salonData.ownerEmail
    } as CompanyUser;

    this.companyUserService.add(ownerModel).subscribe(
      (ownerResponse) => {
        if (ownerResponse.success) {
          // CompanyUser kaydedildikten sonra ID'sini almak için CompanyUser listesini çek
          this.getCompanyUserIdAndCreateRelation(userId, companyId);
        } else {
          this.handleError('Salon sahibi bilgileri kaydı başarısız', WizardStep.OWNER_INFO);
        }
      },
      (error) => {
        this.handleError('Salon sahibi bilgileri kaydı başarısız', WizardStep.OWNER_INFO);
      }
    );
  }

  private getCompanyUserIdAndCreateRelation(userId: number, companyId: number): void {
    this.loadingMessage = 'Kullanıcı-şirket ilişkisi kuruluyor...';

    // CompanyUser'ı email ile bul
    this.companyUserService.getCompanyUsers().subscribe(
      (companyUsersResponse) => {
        if (companyUsersResponse.success) {
          const addedCompanyUser = companyUsersResponse.data.find(cu => cu.email === this.salonData.ownerEmail);
          if (addedCompanyUser) {
            this.createUserCompanyRelation(addedCompanyUser.companyUserID, companyId);
          } else {
            this.handleError('Salon sahibi bulunamadı', WizardStep.OWNER_INFO);
          }
        } else {
          this.handleError('Salon sahibi listesi alınamadı', WizardStep.OWNER_INFO);
        }
      },
      (error) => {
        this.handleError('Salon sahibi listesi alınamadı', WizardStep.OWNER_INFO);
      }
    );
  }

  private createUserCompanyRelation(companyUserId: number, companyId: number): void {
    this.loadingMessage = 'Kullanıcı-şirket ilişkisi kuruluyor...';

    const userCompanyModel = {
      userCompanyId: 0, // Backend'de otomatik atanacak
      userID: companyUserId, // Bu aslında CompanyUser.CompanyUserID olmalı
      companyId: companyId
    } as UserCompany;

    this.userCompanyService.add(userCompanyModel).subscribe(
      (relationResponse) => {
        if (relationResponse.success) {
          this.isSubmitting = false;
          this.toastrService.success('Salon başarıyla kaydedildi!', 'Başarılı');
          this.router.navigate(['/company']);
        } else {
          this.handleError('Kullanıcı-şirket ilişkisi kurulamadı', WizardStep.OWNER_INFO);
        }
      },
      (error) => {
        this.handleError('Kullanıcı-şirket ilişkisi kurulamadı', WizardStep.OWNER_INFO);
      }
    );
  }

  private handleError(message: string, targetStep: WizardStep): void {
    this.isSubmitting = false;
    this.currentStep = targetStep;

    // Hatalı adımdan sonraki adımları tamamlanmamış olarak işaretle
    for (let step = targetStep; step <= WizardStep.PREVIEW; step++) {
      this.completedSteps.delete(step);
    }

    this.toastrService.error(message, 'Hata');
  }

  canSubmitSalon(): boolean {
    return this.isStepCompleted(WizardStep.COMPANY_INFO) &&
           this.isStepCompleted(WizardStep.ADDRESS_INFO) &&
           this.isStepCompleted(WizardStep.OWNER_INFO);
  }

  // Phone validation helper
  isPhoneFieldInvalid(formGroup: FormGroup, fieldName: string): boolean {
    const field = formGroup.get(fieldName);
    return !!(field && field.invalid && (field.dirty || field.touched));
  }

  // Email validation helper
  isEmailFieldInvalid(formGroup: FormGroup, fieldName: string): boolean {
    const field = formGroup.get(fieldName);
    return !!(field && field.invalid && (field.dirty || field.touched));
  }

  // Generic field validation helper
  isFieldInvalid(formGroup: FormGroup, fieldName: string): boolean {
    const field = formGroup.get(fieldName);
    return !!(field && field.invalid && (field.dirty || field.touched));
  }
}
