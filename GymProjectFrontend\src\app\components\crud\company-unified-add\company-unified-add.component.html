<!-- Loading Overlay -->
<div *ngIf="isSubmitting" class="loading-overlay">
  <div class="loading-content">
    <div class="spinner-border text-primary" role="status">
      <span class="visually-hidden">Yükleniyor...</span>
    </div>
    <p class="mt-3 mb-0">Salon kaydediliyor...</p>
  </div>
</div>

<!-- Sticky Header -->
<div class="sticky-header">
  <div class="container-fluid">
    <div class="row align-items-center py-3">
      <div class="col-md-6">
        <div class="d-flex align-items-center">
          <button type="button" class="btn btn-outline-secondary me-3" (click)="goBack()">
            <fa-icon [icon]="faArrowLeft" class="me-1"></fa-icon>
            Geri
          </button>
          <div>
            <h4 class="mb-0">Yeni <PERSON> Ekleme - {{ getStepTitle() }}</h4>
            <small class="text-muted">{{ getStepDescription() }}</small>
          </div>
        </div>
      </div>
      <div class="col-md-6 text-end">
        <div class="d-flex align-items-center justify-content-end">
          <button type="button" class="btn btn-outline-secondary me-2" (click)="goBack()">
            İptal
          </button>
          <button 
            *ngIf="currentStep > WizardStep.COMPANY_INFO" 
            type="button" 
            class="btn btn-outline-primary me-2" 
            (click)="previousStep()">
            <fa-icon [icon]="faArrowLeft" class="me-1"></fa-icon>
            Geri
          </button>
          <button 
            *ngIf="currentStep < WizardStep.PREVIEW" 
            type="button" 
            class="btn btn-primary" 
            (click)="nextStep()">
            İleri
            <fa-icon [icon]="faArrowRight" class="ms-1"></fa-icon>
          </button>
          <button 
            *ngIf="currentStep === WizardStep.PREVIEW" 
            type="button" 
            class="btn btn-success" 
            [disabled]="isSubmitting || !canSubmitSalon()"
            (click)="onSubmit()">
            <fa-icon [icon]="faCheck" class="me-1"></fa-icon>
            SALONU KAYDET
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="main-content">
  <div class="container-fluid">
    
    <!-- Progress Bar -->
    <div class="modern-card mb-4">
      <div class="modern-card-body py-3">
        <div class="progress mb-3" style="height: 8px;">
          <div class="progress-bar bg-primary" 
               role="progressbar" 
               [style.width.%]="getProgress()"
               [attr.aria-valuenow]="getProgress()"
               aria-valuemin="0" 
               aria-valuemax="100">
          </div>
        </div>
        <div class="row text-center">
          <div class="col">
            <small class="text-muted">Adım {{ currentStep }} / 4</small>
          </div>
        </div>
      </div>
    </div>

    <!-- Step Navigation -->
    <div class="modern-card mb-4">
      <div class="modern-card-body py-3">
        <div class="row g-2">
          <div class="col">
            <button
              type="button"
              class="btn w-100 text-start"
              [class]="getStepButtonClass(WizardStep.COMPANY_INFO)"
              [disabled]="!canGoToStep(WizardStep.COMPANY_INFO)"
              (click)="goToStep(WizardStep.COMPANY_INFO)">
              <div class="d-flex align-items-center">
                <div class="me-2">
                  <fa-icon [icon]="getStepIcon(WizardStep.COMPANY_INFO)"></fa-icon>
                </div>
                <div>
                  <div class="fw-bold">1. Şirket Bilgileri</div>
                  <small class="text-muted">Şirket adı ve telefon</small>
                </div>
              </div>
            </button>
          </div>
          <div class="col">
            <button
              type="button"
              class="btn w-100 text-start"
              [class]="getStepButtonClass(WizardStep.ADDRESS_INFO)"
              [disabled]="!canGoToStep(WizardStep.ADDRESS_INFO)"
              (click)="goToStep(WizardStep.ADDRESS_INFO)">
              <div class="d-flex align-items-center">
                <div class="me-2">
                  <fa-icon [icon]="getStepIcon(WizardStep.ADDRESS_INFO)"></fa-icon>
                </div>
                <div>
                  <div class="fw-bold">2. Adres Bilgileri</div>
                  <small class="text-muted">İl, ilçe ve adres</small>
                </div>
              </div>
            </button>
          </div>
          <div class="col">
            <button
              type="button"
              class="btn w-100 text-start"
              [class]="getStepButtonClass(WizardStep.OWNER_INFO)"
              [disabled]="!canGoToStep(WizardStep.OWNER_INFO)"
              (click)="goToStep(WizardStep.OWNER_INFO)">
              <div class="d-flex align-items-center">
                <div class="me-2">
                  <fa-icon [icon]="getStepIcon(WizardStep.OWNER_INFO)"></fa-icon>
                </div>
                <div>
                  <div class="fw-bold">3. Salon Sahibi</div>
                  <small class="text-muted">Kişi bilgileri</small>
                </div>
              </div>
            </button>
          </div>
          <div class="col">
            <button
              type="button"
              class="btn w-100 text-start"
              [class]="getStepButtonClass(WizardStep.PREVIEW)"
              [disabled]="!canGoToStep(WizardStep.PREVIEW)"
              (click)="goToStep(WizardStep.PREVIEW)">
              <div class="d-flex align-items-center">
                <div class="me-2">
                  <fa-icon [icon]="getStepIcon(WizardStep.PREVIEW)"></fa-icon>
                </div>
                <div>
                  <div class="fw-bold">4. Önizleme</div>
                  <small class="text-muted">Kontrol ve kaydet</small>
                </div>
              </div>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Step 1: Company Information -->
    <div *ngIf="currentStep === WizardStep.COMPANY_INFO" class="modern-card mb-4 fade-in">
      <div class="modern-card-header">
        <h5 class="mb-0">
          <fa-icon [icon]="faBuilding" class="me-2"></fa-icon>
          Şirket Bilgileri
        </h5>
      </div>
      <div class="modern-card-body">
        <form [formGroup]="companyForm">
          <div class="row g-3">
            <!-- Company Name -->
            <div class="col-md-6">
              <div class="modern-form-group">
                <label class="modern-form-label">
                  Şirket Adı <span class="text-danger">*</span>
                </label>
                <input
                  type="text"
                  id="companyName"
                  class="modern-form-control"
                  [class.is-invalid]="isFieldInvalid(companyForm, 'companyName')"
                  formControlName="companyName"
                  placeholder="Örn: Fitness Center Spor Salonu">
                <div *ngIf="isFieldInvalid(companyForm, 'companyName')" class="invalid-feedback">
                  <div *ngIf="companyForm.get('companyName')?.hasError('required')">
                    Şirket adı zorunludur
                  </div>
                  <div *ngIf="companyForm.get('companyName')?.hasError('minlength')">
                    Şirket adı en az 2 karakter olmalıdır
                  </div>
                  <div *ngIf="companyForm.get('companyName')?.hasError('maxlength')">
                    Şirket adı en fazla 100 karakter olabilir
                  </div>
                </div>
              </div>
            </div>

            <!-- Company Phone -->
            <div class="col-md-6">
              <div class="modern-form-group">
                <label class="modern-form-label">
                  Şirket Telefonu <span class="text-danger">*</span>
                </label>
                <input
                  type="tel"
                  id="companyPhone"
                  class="modern-form-control"
                  [class.is-invalid]="isPhoneFieldInvalid(companyForm, 'companyPhone')"
                  formControlName="companyPhone"
                  placeholder="05xxxxxxxxx (11 haneli, 0 ile başlamalı)">
                <div *ngIf="isPhoneFieldInvalid(companyForm, 'companyPhone')" class="invalid-feedback">
                  <div *ngIf="companyForm.get('companyPhone')?.hasError('required')">
                    Şirket telefonu zorunludur
                  </div>
                  <div *ngIf="companyForm.get('companyPhone')?.hasError('pattern')">
                    Telefon numarası 11 haneli olmalı ve 0 ile başlamalıdır
                  </div>
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>

    <!-- Step 2: Address Information -->
    <div *ngIf="currentStep === WizardStep.ADDRESS_INFO" class="modern-card mb-4 fade-in">
      <div class="modern-card-header">
        <h5 class="mb-0">
          <fa-icon [icon]="faMapMarkerAlt" class="me-2"></fa-icon>
          Adres Bilgileri
        </h5>
      </div>
      <div class="modern-card-body">
        <form [formGroup]="addressForm">
          <div class="row g-3">
            <!-- City -->
            <div class="col-md-6">
              <div class="modern-form-group">
                <label class="modern-form-label">
                  İl <span class="text-danger">*</span>
                </label>
                <mat-form-field appearance="outline" class="modern-mat-form-field">
                  <input type="text" 
                         matInput 
                         formControlName="city" 
                         [matAutocomplete]="autoCity"
                         (selectionChange)="onCitySelected($event.option.value)"
                         placeholder="İl seçiniz">
                  <mat-autocomplete #autoCity="matAutocomplete" [displayWith]="displayCity">
                    <mat-option *ngFor="let city of filteredCities | async" [value]="city">
                      {{city.cityName}}
                    </mat-option>
                  </mat-autocomplete>
                  <mat-error *ngIf="isFieldInvalid(addressForm, 'city')">
                    İl seçimi zorunludur
                  </mat-error>
                </mat-form-field>
              </div>
            </div>

            <!-- Town -->
            <div class="col-md-6">
              <div class="modern-form-group">
                <label class="modern-form-label">
                  İlçe <span class="text-danger">*</span>
                </label>
                <mat-form-field appearance="outline" class="modern-mat-form-field">
                  <input type="text" 
                         matInput 
                         formControlName="town" 
                         [matAutocomplete]="autoTown"
                         placeholder="İlçe seçiniz">
                  <mat-autocomplete #autoTown="matAutocomplete" [displayWith]="displayTown">
                    <mat-option *ngFor="let town of filteredTowns | async" [value]="town">
                      {{town.townName}}
                    </mat-option>
                  </mat-autocomplete>
                  <mat-error *ngIf="isFieldInvalid(addressForm, 'town')">
                    İlçe seçimi zorunludur
                  </mat-error>
                </mat-form-field>
              </div>
            </div>

            <!-- Address -->
            <div class="col-12">
              <div class="modern-form-group">
                <label class="modern-form-label">
                  Detay Adres <span class="text-danger">*</span>
                </label>
                <textarea
                  id="address"
                  class="modern-form-control"
                  [class.is-invalid]="isFieldInvalid(addressForm, 'address')"
                  formControlName="address"
                  rows="3"
                  placeholder="Mahalle, sokak, bina no vb. detay adres bilgilerini giriniz">
                </textarea>
                <div *ngIf="isFieldInvalid(addressForm, 'address')" class="invalid-feedback">
                  <div *ngIf="addressForm.get('address')?.hasError('required')">
                    Detay adres zorunludur
                  </div>
                  <div *ngIf="addressForm.get('address')?.hasError('maxlength')">
                    Adres en fazla 500 karakter olabilir
                  </div>
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>

    <!-- Step 3: Owner Information -->
    <div *ngIf="currentStep === WizardStep.OWNER_INFO" class="modern-card mb-4 fade-in">
      <div class="modern-card-header">
        <h5 class="mb-0">
          <fa-icon [icon]="faUserTie" class="me-2"></fa-icon>
          Salon Sahibi Bilgileri
        </h5>
        <p class="mb-0 mt-2 text-muted">
          <small>Salon sahibi otomatik olarak sisteme kaydedilecek. Geçici şifre: telefon numarasının son 4 hanesi</small>
        </p>
      </div>
      <div class="modern-card-body">
        <form [formGroup]="ownerForm">
          <div class="row g-3">
            <!-- Owner Name -->
            <div class="col-md-6">
              <div class="modern-form-group">
                <label class="modern-form-label">
                  Ad Soyad <span class="text-danger">*</span>
                </label>
                <input
                  type="text"
                  id="ownerName"
                  class="modern-form-control"
                  [class.is-invalid]="isFieldInvalid(ownerForm, 'ownerName')"
                  formControlName="ownerName"
                  placeholder="Örn: Ali Ayşen Veli Kayseri Bol">
                <div *ngIf="isFieldInvalid(ownerForm, 'ownerName')" class="invalid-feedback">
                  <div *ngIf="ownerForm.get('ownerName')?.hasError('required')">
                    Ad soyad zorunludur
                  </div>
                  <div *ngIf="ownerForm.get('ownerName')?.hasError('minlength')">
                    Ad soyad en az 2 karakter olmalıdır
                  </div>
                  <div *ngIf="ownerForm.get('ownerName')?.hasError('maxlength')">
                    Ad soyad en fazla 100 karakter olabilir
                  </div>
                </div>
              </div>
            </div>

            <!-- Owner Phone -->
            <div class="col-md-6">
              <div class="modern-form-group">
                <label class="modern-form-label">
                  Telefon Numarası <span class="text-danger">*</span>
                </label>
                <input
                  type="tel"
                  id="ownerPhone"
                  class="modern-form-control"
                  [class.is-invalid]="isPhoneFieldInvalid(ownerForm, 'ownerPhone')"
                  formControlName="ownerPhone"
                  placeholder="05xxxxxxxxx (11 haneli, 0 ile başlamalı)">
                <div *ngIf="isPhoneFieldInvalid(ownerForm, 'ownerPhone')" class="invalid-feedback">
                  <div *ngIf="ownerForm.get('ownerPhone')?.hasError('required')">
                    Telefon numarası zorunludur
                  </div>
                  <div *ngIf="ownerForm.get('ownerPhone')?.hasError('pattern')">
                    Telefon numarası 11 haneli olmalı ve 0 ile başlamalıdır
                  </div>
                </div>
              </div>
            </div>

            <!-- Owner Email -->
            <div class="col-12">
              <div class="modern-form-group">
                <label class="modern-form-label">
                  E-posta <span class="text-danger">*</span>
                </label>
                <input
                  type="email"
                  id="ownerEmail"
                  class="modern-form-control"
                  [class.is-invalid]="isEmailFieldInvalid(ownerForm, 'ownerEmail')"
                  formControlName="ownerEmail"
                  placeholder="<EMAIL>">
                <div *ngIf="isEmailFieldInvalid(ownerForm, 'ownerEmail')" class="invalid-feedback">
                  <div *ngIf="ownerForm.get('ownerEmail')?.hasError('required')">
                    E-posta adresi zorunludur
                  </div>
                  <div *ngIf="ownerForm.get('ownerEmail')?.hasError('email')">
                    Geçerli bir e-posta adresi giriniz
                  </div>
                </div>
                <small class="form-text text-muted">
                  Bu e-posta adresi ile salon sahibi sisteme giriş yapabilecek
                </small>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>

    <!-- Step 4: Preview -->
    <div *ngIf="currentStep === WizardStep.PREVIEW" class="modern-card mb-4 fade-in">
      <div class="modern-card-header">
        <h5 class="mb-0">
          <fa-icon [icon]="faEye" class="me-2"></fa-icon>
          Önizleme & Kaydet
        </h5>
        <p class="mb-0 mt-2 text-muted">
          <small>Bilgileri kontrol edin ve salonu kaydetmek için "SALONU KAYDET" butonuna tıklayın</small>
        </p>
      </div>
      <div class="modern-card-body">
        <div class="row g-4">
          <!-- Company Info Summary -->
          <div class="col-md-6">
            <div class="preview-section">
              <h6 class="preview-section-title">
                <fa-icon [icon]="faBuilding" class="me-2"></fa-icon>
                Şirket Bilgileri
              </h6>
              <div class="preview-item">
                <strong>Şirket Adı:</strong> {{ salonData.companyName }}
              </div>
              <div class="preview-item">
                <strong>Şirket Telefonu:</strong> {{ salonData.companyPhone }}
              </div>
            </div>
          </div>

          <!-- Address Info Summary -->
          <div class="col-md-6">
            <div class="preview-section">
              <h6 class="preview-section-title">
                <fa-icon [icon]="faMapMarkerAlt" class="me-2"></fa-icon>
                Adres Bilgileri
              </h6>
              <div class="preview-item">
                <strong>İl:</strong> {{ salonData.city?.cityName }}
              </div>
              <div class="preview-item">
                <strong>İlçe:</strong> {{ salonData.town?.townName }}
              </div>
              <div class="preview-item">
                <strong>Detay Adres:</strong> {{ salonData.address }}
              </div>
            </div>
          </div>

          <!-- Owner Info Summary -->
          <div class="col-12">
            <div class="preview-section">
              <h6 class="preview-section-title">
                <fa-icon [icon]="faUserTie" class="me-2"></fa-icon>
                Salon Sahibi Bilgileri
              </h6>
              <div class="row">
                <div class="col-md-4">
                  <div class="preview-item">
                    <strong>Ad Soyad:</strong> {{ salonData.ownerName }}
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="preview-item">
                    <strong>Telefon:</strong> {{ salonData.ownerPhone }}
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="preview-item">
                    <strong>E-posta:</strong> {{ salonData.ownerEmail }}
                  </div>
                </div>
              </div>
              <div class="alert alert-info mt-3">
                <fa-icon [icon]="faInfoCircle" class="me-2"></fa-icon>
                <strong>Önemli:</strong> Salon sahibi otomatik olarak sisteme kaydedilecek.
                Geçici şifre telefon numarasının son 4 hanesi olacak ve ilk girişte şifre değiştirmesi istenecek.
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

  </div>
</div>
