/* Company Unified Add Wizard Styles */

/* Loading Overlay */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.loading-content {
  background-color: var(--bg-primary);
  padding: 2rem;
  border-radius: var(--border-radius-lg);
  text-align: center;
  box-shadow: var(--shadow-lg);
}

/* Sticky Header */
.sticky-header {
  position: sticky;
  top: 0;
  z-index: 1020;
  background-color: var(--bg-primary);
  border-bottom: 1px solid var(--border-color);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all var(--transition-speed) var(--transition-timing);
}

[data-theme="dark"] .sticky-header {
  background-color: var(--bg-primary);
  border-bottom-color: var(--border-color);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.main-content {
  padding-top: 0;
}

/* Progress Bar Enhancements */
.progress {
  background-color: rgba(var(--primary-rgb), 0.1);
  border-radius: var(--border-radius-pill);
}

.progress-bar {
  transition: width 0.6s ease;
  border-radius: var(--border-radius-pill);
}

/* Step Navigation Buttons */
.step-nav-btn {
  border-radius: var(--border-radius-md);
  transition: all var(--transition-speed) var(--transition-timing);
  min-height: 60px;
  text-align: left;
}

.step-nav-btn.active {
  background-color: var(--primary);
  border-color: var(--primary);
  color: white;
  box-shadow: var(--shadow-sm);
}

.step-nav-btn.completed {
  background-color: var(--success);
  border-color: var(--success);
  color: white;
}

.step-nav-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

.step-nav-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Form Styling */
.modern-form-group {
  margin-bottom: 1.5rem;
}

.modern-form-label {
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
  display: block;
}

.modern-form-control {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
  background-color: var(--bg-primary);
  color: var(--text-primary);
  transition: all var(--transition-speed) var(--transition-timing);
}

.modern-form-control:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 0.2rem rgba(var(--primary-rgb), 0.25);
}

.modern-form-control.is-invalid {
  border-color: var(--danger);
}

.modern-form-control.is-invalid:focus {
  box-shadow: 0 0 0 0.2rem rgba(var(--danger-rgb), 0.25);
}

.modern-form-control.is-valid {
  border-color: var(--success);
}

.modern-form-control.is-valid:focus {
  box-shadow: 0 0 0 0.2rem rgba(var(--success-rgb), 0.25);
}

/* Real-time validation feedback */
.invalid-feedback {
  display: block;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 0.875rem;
  color: var(--danger);
}

.valid-feedback {
  display: block;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 0.875rem;
  color: var(--success);
}

/* Material Form Field Overrides */
.modern-mat-form-field {
  width: 100%;
}

::ng-deep .modern-mat-form-field.mat-form-field-appearance-outline .mat-form-field-outline {
  color: var(--border-color);
}

::ng-deep .modern-mat-form-field.mat-form-field-appearance-outline.mat-focused .mat-form-field-outline-thick {
  color: var(--primary);
}

::ng-deep .modern-mat-form-field .mat-form-field-label {
  color: var(--text-secondary);
}

::ng-deep .modern-mat-form-field.mat-focused .mat-form-field-label {
  color: var(--primary);
}

/* Preview Section Styling */
.preview-section {
  background-color: var(--bg-secondary);
  border-radius: var(--border-radius-md);
  padding: 1.5rem;
  margin-bottom: 1rem;
  transition: all var(--transition-speed) var(--transition-timing);
}

.preview-section:hover {
  box-shadow: var(--shadow-sm);
  transform: translateY(-2px);
}

.preview-section-title {
  color: var(--primary);
  font-weight: 600;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  border-bottom: 2px solid var(--primary-light);
  padding-bottom: 0.5rem;
}

.preview-item {
  margin-bottom: 0.75rem;
  padding: 0.5rem 0;
  border-bottom: 1px solid var(--border-color);
}

.preview-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Dark Mode Enhancements */
[data-theme="dark"] .modern-form-control {
  background-color: var(--bg-secondary);
  border-color: var(--border-color);
  color: var(--text-primary);
}

[data-theme="dark"] .modern-form-control:focus {
  background-color: var(--bg-secondary);
  border-color: var(--primary);
}

[data-theme="dark"] .preview-section {
  background-color: var(--bg-tertiary);
  border-color: var(--border-color);
}

[data-theme="dark"] .step-nav-btn.completed {
  background-color: var(--success-light);
  color: var(--success);
}

[data-theme="dark"] .loading-content {
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-color);
}

/* Responsive Design */
@media (max-width: 768px) {
  .sticky-header .row {
    flex-direction: column;
  }

  .sticky-header .col-md-6:last-child {
    margin-top: 1rem;
  }

  .step-nav-btn {
    min-height: 50px;
    font-size: 0.875rem;
  }

  .preview-section {
    padding: 1rem;
  }
}
